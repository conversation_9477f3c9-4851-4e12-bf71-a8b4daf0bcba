import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import GoBack from '@/components/GoBack';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import useEventStore from '@/store/eventStore';
import axios from 'axios';
import { appDomain } from '@/constants';
import { toast } from 'sonner';
import Wave from '@/components/Wave';

interface SearchPeopleType {
    _id: string;
    firstName: string;
    lastName: string;
    linkedinUrl: string;
    designation: string;
    company: string;
    industry: string;
    city: string;
    employeeSize: string;
}


const cities: string[] = [
    "Mumbai", "Delhi", "Bengaluru", "Gurgaon", "Chennai", "Pune",
    "Hyderabad", "Noida", "New Delhi", "Ahmedabad", "Jaipur", "Kolkata",
    "Patna", "Visakhapatnam", "Lucknow", "Bhopal", "Chandigarh", "Mohali"
];

const SearchPeople: React.FC = () => {
    const { slug } = useParams<{ slug: string }>();
    const event = useEventStore((state) => state.getEventBySlug(slug));
    const [designation, setDesignation] = useState('');
    const [selectedCity, setSelectedCity] = useState('');
    const [loading, setLoading] = useState(false);

    const [people, setPeople] = useState<SearchPeopleType[]>([]);

    const handleSearch = async () => {
        // TODO: Implement search functionality
        setLoading(true);
        const response = await axios.post(`${appDomain}/api/mapping/v1/people/search-people`, {
            designation,
            city: selectedCity
        }, {
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.data.status) {
            toast("People searched successfully", {
                className: "!bg-green-800 !text-white !font-sans !font-regular tracking-wider"
            });
            setPeople(response.data.data.peoplesWithCompanySize);
        } else {
            toast(response.data.message || "Failed to search people", {
                className: "!bg-red-800 !text-white !font-sans !font-regular tracking-wider"
            });
        }
        setLoading(false);
    };

    if (loading) {
        return <Wave />
    }

    return (
        <div className="space-y-6">
            <div className='flex gap-5 items-center'>
                <GoBack />
                <h1 className='text-xl font-semibold'>{event?.title}</h1>
            </div>

            <div className="flex flex-row max-w-2xl mx-auto items-end gap-4">
                <div className="flex flex-col gap-2 w-full">
                    <Label className="font-semibold" htmlFor="designation">
                        Designation
                    </Label>
                    <Input
                        id="designation"
                        type="text"
                        value={designation}
                        onChange={(e) => setDesignation(e.target.value)}
                        className="input max-w-[200px] text-base"
                        placeholder="Enter designation"
                    />
                </div>

                <div className="flex flex-col gap-2 w-full">
                    <Label className="font-semibold" htmlFor="city">
                        City
                    </Label>
                    <Select
                        value={selectedCity}
                        onValueChange={setSelectedCity}
                    >
                        <SelectTrigger className="input max-w-[200px] text-base">
                            <SelectValue placeholder="Select a city" />
                        </SelectTrigger>
                        <SelectContent>
                            {cities.map((city) => (
                                <SelectItem
                                    key={city}
                                    value={city}
                                    className="cursor-pointer"
                                >
                                    {city}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <Button
                    onClick={handleSearch}
                    className="w-fit btn"
                >
                    Search
                </Button>
            </div>
        </div>
    );
};

export default SearchPeople;
